import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/auth/confirmation_body.dart';
import 'package:korrency/ui/screens/screens.dart';

class AppRouters {
  static Route<dynamic> getRoute(RouteSettings settings) {
    // GlobalVar.activeRoute = settings.name;

    switch (settings.name) {
      // Initial/Splash screens - Fade transition
      case RoutePath.welcomeBackScreen:
        bool fromSplash =
            settings.arguments is bool ? settings.arguments as bool : true;
        return TransitionUtils.buildTransition(
          WelcomeBackScreen(fromSplash: fromSplash),
          settings,
        );

      case RoutePath.splashScreen:
        return TransitionUtils.buildTransition(
          const SplashScreen(),
          settings,
        );

      case RoutePath.introScreen:
        return TransitionUtils.buildTransition(
          const IntroScreen(),
          settings,
        );

      case RoutePath.updateAvailableScreen:
        return TransitionUtils.buildTransition(
          const UpdateAvailableScreen(),
          settings,
        );

      case RoutePath.createFreshDeskTicketWebview:
        final arg = settings.arguments;
        if (arg is WebViewArg) {
          return TransitionUtils.buildTransition(
            CreateFreshdeskTicketWebview(arg: arg),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.createAcctScreen:
        return TransitionUtils.buildTransition(
          const CreateAccountScreen(),
          settings,
        );

      case RoutePath.verifyPhoneNumScreen:
        return TransitionUtils.buildTransition(
          const VerifyPhoneNumberScreen(),
          settings,
        );

      case RoutePath.emailAndPasswordScreen:
        return TransitionUtils.buildTransition(
          const EmailAndPasswordScreen(),
          settings,
        );

      case RoutePath.verifyEmailScreen:
        return TransitionUtils.buildTransition(
          const VerifyEmailAddress(),
          settings,
        );

      case RoutePath.biometricScreen:
        return TransitionUtils.buildTransition(
          const BiometricScreen(),
          settings,
        );

      case RoutePath.frequentRecipientCountryScreen:
        final arg =
            settings.arguments is bool ? settings.arguments as bool : false;
        return TransitionUtils.buildTransition(
          FrequentRecipientCountryScreen(fromHome: arg),
          settings,
        );
      case RoutePath.successScreen:
        final args = settings.arguments;
        if (args is ConfirmationArg) {
          return TransitionUtils.buildTransition(
            SuccessScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.successConfirmScreen:
        final args = settings.arguments;
        if (args is SuccessConfirmArg) {
          return TransitionUtils.buildTransition(
            SuccessConfirmScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      // Login flow
      case RoutePath.loginScreen:
        return TransitionUtils.buildTransition(
          const LoginScreen(),
          settings,
        );

      case RoutePath.forgotPasswordScreen:
        return TransitionUtils.buildTransition(
          const ForgotPasswordScreen(),
          settings,
        );

      case RoutePath.createNewPasswordScreen:
        return TransitionUtils.buildTransition(
          const CreateNewPasswordScreen(),
          settings,
        );

      case RoutePath.passwordResetScreen:
        return TransitionUtils.buildTransition(
          const PasswordResetScreen(),
          settings,
        );

      case RoutePath.notifcationScreen:
        return TransitionUtils.buildTransition(
          const NotificationScreen(),
          settings,
        );

      case RoutePath.preferredMehodScreen:
        return TransitionUtils.buildTransition(
          const PreferredMethodScreen(),
          settings,
        );

      case RoutePath.interacTransferScreen:
        return TransitionUtils.buildTransition(
          const InteracTransferScreen(),
          settings,
        );

      case RoutePath.anotherInteracEmailScreen:
        return TransitionUtils.buildTransition(
          const AnotherInteracEmailScreen(),
          settings,
        );

      case RoutePath.verifyInteracEmailScreen:
        final args = settings.arguments;
        if (args is String) {
          return TransitionUtils.buildTransition(
            VerifyInteracEmailScreen(email: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.interacEmailConfirmationScreen:
        final arg = settings.arguments;
        if (arg is StatementConfirmScreenArgs) {
          return TransitionUtils.buildTransition(
            StatementConfirmationScreen(args: arg),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.accountDetailsScreen:
        return TransitionUtils.buildTransition(
          const AccountDetailsScreen(),
          settings,
        );

      case RoutePath.accountLimitScreen:
        return TransitionUtils.buildTransition(
          const AccounntLimitScreen(),
          settings,
        );

      case RoutePath.shareByUsernameScreen:
        return TransitionUtils.buildTransition(
          const ShareByUsernameScreen(),
          settings,
        );

      case RoutePath.accountStatementScreen:
        return TransitionUtils.buildTransition(
          const AccountStatementScreen(),
          settings,
        );

      case RoutePath.convertCurrencyScreen:
        return TransitionUtils.buildTransition(
          const ConvertCurrencyScreen(),
          settings,
        );

      case RoutePath.sendMoneyScreen:
        final args = settings.arguments as SendMoneyArg?;
        return TransitionUtils.buildTransition(
          SendMoneyScreen(arg: args),
          settings,
        );

      case RoutePath.sendToNgnScreen:
        if (settings.arguments is PaymentMethod) {
          return TransitionUtils.buildTransition(
            SendBankTransferScreen(
              paymentMethod: settings.arguments as PaymentMethod,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.sendToCadScreen:
        return TransitionUtils.buildTransition(
          const SendToCadScreen(),
          settings,
        );

      case RoutePath.reviewScreen:
        var arg =
            settings.arguments is bool ? settings.arguments as bool : false;
        return TransitionUtils.buildTransition(
          SendMoneyReviewScreen(isKorrencyUser: arg),
          settings,
        );

      case RoutePath.sendMoneyMethodScreen:
        return TransitionUtils.buildTransition(
          const SendMoneyMethodScreen(),
          settings,
        );

      case RoutePath.sendMoneyAuthorizeScreen:
        return TransitionUtils.buildTransition(
          const SendMoneyAuthorizeScreen(),
          settings,
        );

      case RoutePath.sendMoneyMobileMoney:
        if (settings.arguments is BeneficiaryPaymentArg) {
          return TransitionUtils.buildTransition(
            MobileMoneyScreen(
              arg: settings.arguments as BeneficiaryPaymentArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.infoBeneficiaryScreen:
        return TransitionUtils.buildTransition(
          const InfoBeneficiaryScreen(),
          settings,
        );

      case RoutePath.selectCountryScreen:
        return TransitionUtils.buildTransition(
          const SelectCountryScreen(),
          settings,
        );

      case RoutePath.bankTransferScreen:
        if (settings.arguments is BeneficiaryPaymentArg) {
          return TransitionUtils.buildTransition(
            BankTransferScreen(
              arg: settings.arguments as BeneficiaryPaymentArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.mobileMoneyScreen:
        if (settings.arguments is BeneficiaryPaymentArg) {
          return TransitionUtils.buildTransition(
            MobileMoneyScreen(
              arg: settings.arguments as BeneficiaryPaymentArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.beneficiaryScreen:
        var arg = settings.arguments as BeneficiaryArg?;
        return TransitionUtils.buildTransition(
          BeneficiaryScreen(arg: arg),
          settings,
        );

      case RoutePath.beneficiaryPreferredPaymentScreen:
        if (settings.arguments is Currency) {
          return TransitionUtils.buildTransition(
            BeneficiaryPreferredMethodScreen(
              currency: settings.arguments as Currency,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.newBeneficiaryScreen:
        if (settings.arguments is Currency) {
          return TransitionUtils.buildTransition(
            NewBeneficiaryScreen(
              currency: settings.arguments as Currency,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      // Additional routes from commented section

      case RoutePath.authorizeTransaction:
        if (settings.arguments is OfferTypeArg) {
          return TransitionUtils.buildTransition(
            AuthorizeTransactionScreen(
              args: settings.arguments as OfferTypeArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.referralScreen:
        return TransitionUtils.buildTransition(
          const ReferralScreen(),
          settings,
        );

      case RoutePath.exchangeRateScreen:
        return TransitionUtils.buildTransition(
          const ExchangeRateScreen(),
          settings,
        );

      case RoutePath.navigateExchangeOffers:
        return TransitionUtils.buildTransition(
          const NavigateExchangeOffers(),
          settings,
        );

      case RoutePath.sendToKorrencyUser:
        return TransitionUtils.buildTransition(
          const KorrencyUserScreen(),
          settings,
        );

      case RoutePath.transactionScreen:
        return TransitionUtils.buildTransition(
          const TransactionsScreen(),
          settings,
        );

      case RoutePath.transactionStatusScreen:
        final args = settings.arguments;
        if (args is ConfirmationArg) {
          return TransitionUtils.buildTransition(
            TransactionStatusScreen(arg: args),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.transactionDetailsScreen:
        if (settings.arguments is TransactionArg) {
          return TransitionUtils.buildTransition(
            TransactionDetailsScreen(
              transactionArg: settings.arguments as TransactionArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.securityScreen:
        return TransitionUtils.buildTransition(
          const SecurityScreen(),
          settings,
        );

      case RoutePath.answerQuestionScreen:
        return TransitionUtils.buildTransition(
          const AnswerQuestionScreen(),
          settings,
        );

      case RoutePath.confirmYourPinScreen:
        return TransitionUtils.buildTransition(
          const ConfirmYourPinScreen(),
          settings,
        );

      case RoutePath.setupYourPinScreen:
        return TransitionUtils.buildTransition(
          const SetupYourPinScreen(),
          settings,
        );

      case RoutePath.newPasswordScreen:
        return TransitionUtils.buildTransition(
          const NewPasswordScreen(),
          settings,
        );

      case RoutePath.changePasswordScreen:
        return TransitionUtils.buildTransition(
          const ChangePasswordScreen(),
          settings,
        );

      case RoutePath.changeEmailScreen:
        return TransitionUtils.buildTransition(
          const ChangeEmailScreen(),
          settings,
        );

      case RoutePath.newEmailScreen:
        return TransitionUtils.buildTransition(
          const NewEmailScreen(),
          settings,
        );

      case RoutePath.changeSuccessScreen:
        return TransitionUtils.buildTransition(
          const ChangeSuccessScreen(),
          settings,
        );

      case RoutePath.changeErrorScreen:
        return TransitionUtils.buildTransition(
          const ChangeErrorScreen(),
          settings,
        );

      case RoutePath.newPhoneNumberScreen:
        return TransitionUtils.buildTransition(
          const NewPhoneNumberScreen(),
          settings,
        );

      case RoutePath.myDeviceScreen:
        return TransitionUtils.buildTransition(
          const MyDevicesScreen(),
          settings,
        );

      case RoutePath.verifyTrustedDeviceScreen:
        return TransitionUtils.buildTransition(
          const VerifyTrustedDevicePinScreen(),
          settings,
        );

      case RoutePath.twoFactorAuthScreen:
        return TransitionUtils.buildTransition(
          const TwoFactorAuthScreen(),
          settings,
        );

      case RoutePath.securityQuestionScreen:
        return TransitionUtils.buildTransition(
          const SecurityQuestionsScreen(),
          settings,
        );

      case RoutePath.aboutUsScreen:
        return TransitionUtils.buildTransition(
          const AboutUsScreen(),
          settings,
        );

      case RoutePath.preferenceScreen:
        return TransitionUtils.buildTransition(
          const PreferenceScreen(),
          settings,
        );

      case RoutePath.helpSupportScreen:
        return TransitionUtils.buildTransition(
          const HelpSupportScreen(),
          settings,
        );

      case RoutePath.helpVideoScreen:
        return TransitionUtils.buildTransition(
          const HelpVideoScreen(),
          settings,
        );

      case RoutePath.reactOutToUsScreen:
        return TransitionUtils.buildTransition(
          const ReachOutToUsScreen(),
          settings,
        );

      case RoutePath.videoPlayerScreen:
        if (settings.arguments is VideoPlayerArg) {
          return TransitionUtils.buildTransition(
            VideoPlayerScreen(
              args: settings.arguments as VideoPlayerArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.profileDetailsScreen:
        return TransitionUtils.buildTransition(
          const ProfileDetailsScreen(),
          settings,
        );

      case RoutePath.profileScreen:
        return TransitionUtils.buildTransition(
          const ProfileScreen(),
          settings,
        );

      case RoutePath.selectAvatarScreen:
        return TransitionUtils.buildTransition(
          const SelectAvatarScreen(),
          settings,
        );

      case RoutePath.previewAvatarScreen:
        if (settings.arguments is String) {
          return TransitionUtils.buildTransition(
            PreviewAvatarScreen(
              avatarString: settings.arguments as String,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.deactivateAccountScreen:
        return TransitionUtils.buildTransition(
          const DeactivateAccountScreen(),
          settings,
        );

      case RoutePath.deactivateAccountReasonScreen:
        return TransitionUtils.buildTransition(
          const DeactivateAccountReasonScreen(),
          settings,
        );

      case RoutePath.howOfferWorks:
        return TransitionUtils.buildTransition(
          const HowItWorksScreen(),
          settings,
        );

      case RoutePath.createOfferScreen:
        return TransitionUtils.buildTransition(
          const CreateOfferScreen(),
          settings,
        );

      case RoutePath.createBuyOfferScreen:
        if (settings.arguments is OfferTypeArg) {
          return TransitionUtils.buildTransition(
            CreateOfferPageviewScreen(
              args: settings.arguments as OfferTypeArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.reviewOfferScreen:
        if (settings.arguments is OfferTypeArg) {
          return TransitionUtils.buildTransition(
            ReviewOfferScreen(
              args: settings.arguments as OfferTypeArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.offerTransactionStatusScreen:
        return TransitionUtils.buildTransition(
          const OfferTransactionStatusScreen(),
          settings,
        );

      case RoutePath.offerDetailsScreen:
        if (settings.arguments is OfferDetailArg) {
          return TransitionUtils.buildTransition(
            OfferDetailsScreen(
              arg: settings.arguments as OfferDetailArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.myOfferDetailsScreen:
        if (settings.arguments is OfferDetailArg) {
          return TransitionUtils.buildTransition(
            MyOfferDetailsScreen(
              arg: settings.arguments as OfferDetailArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.p2ptransferScreen:
        if (settings.arguments is P2pTransferArg) {
          return TransitionUtils.buildTransition(
            P2PTransferScreen(
              arg: settings.arguments as P2pTransferArg,
            ),
            settings,
          );
        }
        return errorScreen(settings);

      case RoutePath.p2pReviewScreen:
        return TransitionUtils.buildTransition(
          const P2PReviewScreen(),
          settings,
        );

      case RoutePath.p2pPinAuthorizeScreen:
        return TransitionUtils.buildTransition(
          const P2pPinAuthorizeScreen(),
          settings,
        );

      case RoutePath.setRateAlertScreen:
        return TransitionUtils.buildTransition(
          const SetRateAlertScreen(),
          settings,
        );

      case RoutePath.setAlertScreen:
        return TransitionUtils.buildTransition(
          const SetAlertScreen(),
          settings,
        );

      case RoutePath.createAlertScreen:
        return TransitionUtils.buildTransition(
          const CreateAlertScreen(),
          settings,
        );

      case RoutePath.sortOffersScreen:
        return TransitionUtils.buildTransition(
          const SortOffersScreen(),
          settings,
        );

      case RoutePath.yourOfferScreen:
        return TransitionUtils.buildTransition(
          const YourOfferScreen(),
          settings,
        );

      case RoutePath.marketPlaceScreen:
        return TransitionUtils.buildTransition(
          const MarketPlaceScreen(),
          settings,
        );

      case RoutePath.dashboardNav:
        int index = settings.arguments is int ? settings.arguments as int : 0;
        return TransitionUtils.buildTransition(
          DashboardNav(index: index),
          settings,
        );

      default:
        return errorScreen(settings);
    }
  }

  static errorScreen(RouteSettings settings) {
    return TransitionUtils.buildTransition(
      ScreenNotFound(routeName: settings.name),
      settings,
    );
  }
}
