import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';

class ChangeSuccessScreen extends StatelessWidget {
  const ChangeSuccessScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(36)),
        child: Center(
            child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              AppSvgs.emailSuccess,
              height: Sizer.height(292),
            ),
            YBox(36),
            Text(
              'Email Changed',
              style: FontTypography.text26.semiBold,
            ),
            YBox(4),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: 'Successfully!  ',
                    style: FontTypography.text16.semiBold.withCustomColor(
                      AppColors.gray79,
                    ),
                  ),
                  TextSpan(
                    text: '\nYour Email has been changed successfully',
                    style: FontTypography.text16.copyWith(
                      color: AppColors.grayAB,
                    ),
                  ),
                ],
              ),
            ),
          ],
        )),
      ),
    );
  }
}
