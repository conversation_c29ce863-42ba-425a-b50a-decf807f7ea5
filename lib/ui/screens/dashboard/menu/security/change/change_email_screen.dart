import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ChangeEmailScreen extends StatefulWidget {
  const ChangeEmailScreen({super.key});

  @override
  State<ChangeEmailScreen> createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends State<ChangeEmailScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadQuestions();
    });
  }

    _loadQuestions() {
    if (context.read<SecQuestVM>().secQuestions.isEmpty) {
      context.read<SecQuestVM>().getSecurityQuestions();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'Change Email',
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(24),
          vertical: Sizer.height(10),
        ),
        children: [
          Text(
            "To change your email, please provide the answer to the security questions you set",
            style: FontTypography.text16.withCustomColor(AppColors.gray93),
          ),
          YBox(40),
          Container(
            padding: EdgeInsets.symmetric(
              vertical: Sizer.height(16),
              horizontal: Sizer.width(28),
            ),
            decoration: BoxDecoration(
              color: AppColors.grayFE,
              borderRadius: BorderRadius.circular(Sizer.height(12)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "Question 1",
                        style: FontTypography.text12
                            .withCustomColor(AppColors.gray93),
                      ),
                      YBox(8),
                      Container(
                        height: Sizer.height(1),
                        width: Sizer.width(90),
                        color: AppColors.grayEC,
                      ),
                    ],
                  ),
                ),
                YBox(20),
                Text(
                  "What is the title of your \nfavorite book?",
                  style: FontTypography.text22.semiBold,
                ),
                YBox(70),
                Text(
                  "Your answer",
                  style:
                      FontTypography.text12.withCustomColor(AppColors.gray93),
                ),
                YBox(8),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.grayAB),
                    borderRadius: BorderRadius.circular(Sizer.height(12)),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          left: Sizer.width(12),
                          top: Sizer.height(10),
                        ),
                        child: Icon(
                          Iconsax.message_edit,
                          color: AppColors.gray500,
                          size: Sizer.height(20),
                        ),
                      ),
                      Expanded(
                        child: CustomTextField(
                          hideBorder: true,
                          borderRadius: Sizer.height(12),
                          maxLines: 3,
                          contentPadding: EdgeInsets.only(
                            left: Sizer.width(8),
                            top: Sizer.height(20),
                          ),
                          onChanged: (val) {},
                        ),
                      ),
                    ],
                  ),
                ),
                YBox(40),
              ],
            ),
          ),
          YBox(160),
          CustomBtn.withChild(
            borderRadius: BorderRadius.circular(Sizer.radius(20)),
            onTap: () {
              Navigator.pushNamed(context, RoutePath.newEmailScreen);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Continue',
                  style: FontTypography.text15.medium
                      .withCustomColor(AppColors.white),
                ),
                const XBox(8),
                const Icon(
                  Icons.arrow_forward,
                  color: AppColors.white,
                  size: 20,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
