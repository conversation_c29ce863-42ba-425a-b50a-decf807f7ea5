import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';

class ChangeErrorScreen extends StatelessWidget {
  const ChangeErrorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(36)),
        child: Center(
            child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              AppSvgs.emailError,
              height: Sizer.height(292),
            ),
            // YBox(36),
            Text(
              'Uh Oh!',
              style: FontTypography.text26.semiBold,
            ),
            YBox(4),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                children: [
                  TextSpan(
                    text: 'SucAttempt unsuccessful!cessfully! ',
                    style: FontTypography.text16.semiBold.withCustomColor(
                      AppColors.gray79,
                    ),
                  ),
                  TextSpan(
                    text:
                        '\nSorry we are unable to change your email at this time, please try later',
                    style: FontTypography.text16.copyWith(
                      color: AppColors.grayAB,
                    ),
                  ),
                ],
              ),
            ),
          ],
        )),
      ),
    );
  }
}
