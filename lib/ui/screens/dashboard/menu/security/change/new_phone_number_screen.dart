import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NewPhoneNumberScreen extends StatefulWidget {
  const NewPhoneNumberScreen({super.key});

  @override
  State<NewPhoneNumberScreen> createState() => _NewPhoneNumberScreenState();
}

class _NewPhoneNumberScreenState extends State<NewPhoneNumberScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'New Phone Number',
      ),
      body: Stack(
        children: [
          ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
              vertical: Sizer.height(10),
            ),
            children: [
              Text(
                "Your Korrency account information would be updated to this email",
                style: FontTypography.text16.withCustomColor(AppColors.gray93),
              ),
              YBox(30),
              Consumer<OnBoardVM>(builder: (context, vm, _) {
                return Row(
                  children: [
                    Expanded(
                      flex: 4,
                      child: CustomTextField(
                        labelText: "Country",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        isReadOnly: true,
                        suffixIcon: Icon(
                          Iconsax.lock,
                          color: AppColors.gray500,
                          size: Sizer.height(16),
                        ),
                        prefixIcon: InkWell(
                          onTap: () {
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: const SelectCountrySheet(),
                            );
                          },
                          child: Container(
                            padding: EdgeInsets.only(
                              left: Sizer.width(10),
                              right: Sizer.width(6),
                            ),
                            child: CurrencyPrefix(
                              showArrow: false,
                              countryDialCode: vm.country?.dialCode ?? "+1",
                              countryFlag: vm.country?.flag ?? "",
                              countryCodeStyle: FontTypography.text16
                                  .withCustomColor(AppColors.gray93),
                            ),
                          ),
                        ),
                        onChanged: (val) {},
                      ),
                    ),
                    const XBox(10),
                    Expanded(
                      flex: 10,
                      child: CustomTextField(
                        labelText: "Phone Number",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        onChanged: (val) {},
                      ),
                    ),
                  ],
                );
              }),
              YBox(40),
            ],
          ),
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: CustomBtn.withChild(
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                onTap: () {
                  BsWrapper.bottomSheet(
                    context: context,
                    widget: const ConfirmEmailModal(),
                  );
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Continue',
                      style: FontTypography.text15.medium
                          .withCustomColor(AppColors.white),
                    ),
                    const XBox(8),
                    const Icon(
                      Icons.arrow_forward,
                      color: AppColors.white,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
